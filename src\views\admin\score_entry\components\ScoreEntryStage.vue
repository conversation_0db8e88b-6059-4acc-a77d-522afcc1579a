<template>
  <div class="space-y-6">
    <!-- Filter Summary Bar -->
    <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-maneb-primary">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Selected Criteria</h3>
          <div class="text-sm text-gray-600 space-y-1">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
              <span><strong>Division:</strong> {{ getDivisionName() }}</span>
              <span><strong>District:</strong> {{ getDistrictName() }}</span>
              <span><strong>Center:</strong> {{ getCenterName() }}</span>
              <span><strong>Exam Type:</strong> {{ getExamTypeName() }}</span>
              <span><strong>School:</strong> {{ getSchoolName() }}</span>
              <span><strong>Subject:</strong> {{ getSubjectName() }}</span>
              <span><strong>Paper:</strong> {{ getPaperName() }}</span>
              <span><strong>Score Type:</strong> {{ getScoreTypeName() }}</span>
            </div>
          </div>
        </div>
        <button
          @click="editFilters"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-maneb-primary bg-white border border-maneb-primary rounded-lg hover:bg-maneb-primary hover:text-white focus:ring-4 focus:outline-none focus:ring-maneb-primary/50 transition-colors duration-200"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          Edit Filters
        </button>
      </div>
    </div>

    <!-- Exam Number Selection -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Select Exam Number</h3>
        <span v-if="selectedExamNumber" class="text-sm text-green-600 font-medium">
          ✓ {{ getSelectedExamNumberName() }}
        </span>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="examNumber" class="block text-sm font-medium text-gray-700 mb-2">
            Exam Number <span class="text-red-500">*</span>
          </label>
          <select
            id="examNumber"
            v-model="selectedExamNumber"
            @change="onExamNumberChange"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5"
          >
            <option value="">Select Exam Number</option>
            <option v-for="examNumber in availableExamNumbers" :key="examNumber.id" :value="examNumber.id">
              {{ examNumber.name }}
            </option>
          </select>
        </div>

        <div v-if="selectedExamNumber" class="flex items-end">
          <button
            @click="loadStudentsForExam"
            :disabled="isLoading"
            class="w-full inline-flex items-center justify-center px-4 py-2.5 text-sm font-medium text-white bg-maneb-primary rounded-lg hover:bg-maneb-primary-dark focus:ring-4 focus:outline-none focus:ring-maneb-primary/50 disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            {{ isLoading ? 'Loading Students...' : 'Load Students' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Score Entry Table -->
    <div v-if="studentsLoaded" class="bg-white rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <div>
            <h3 class="text-lg font-medium text-gray-900">Score Entry</h3>
            <p class="text-sm text-gray-500 mt-1">Enter scores for eligible students</p>
          </div>
          <div class="flex items-center space-x-3">
            <div class="text-sm text-gray-500">
              <span>{{ eligibleStudents.length }} students found</span>
              <span class="mx-2">•</span>
              <span>{{ completedScores }}/{{ eligibleStudents.length }} scores entered</span>
            </div>
            <button
              @click="saveAllScores"
              :disabled="!hasUnsavedChanges || isSaving"
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 focus:ring-4 focus:outline-none focus:ring-green-300 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <svg v-if="isSaving" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
              </svg>
              {{ isSaving ? 'Saving...' : 'Save All Changes' }}
            </button>
          </div>
        </div>

        <!-- Progress Bar -->
        <div v-if="eligibleStudents.length > 0" class="mb-4">
          <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>Score Entry Progress</span>
            <span>{{ Math.round((completedScores / eligibleStudents.length) * 100) }}% Complete</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="bg-maneb-primary h-2 rounded-full transition-all duration-300"
              :style="{ width: `${(completedScores / eligibleStudents.length) * 100}%` }"
            ></div>
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full text-left text-sm text-gray-500">
          <thead class="bg-gray-50 text-xs uppercase text-gray-700">
            <tr>
              <th scope="col" class="px-6 py-3">Student ID</th>
              <th scope="col" class="px-6 py-3">Exam Number</th>
              <th scope="col" class="px-6 py-3">Student Name</th>
              <th scope="col" class="px-6 py-3 hidden md:table-cell">Subject Name</th>
              <th scope="col" class="px-6 py-3">Score</th>
              <th scope="col" class="px-6 py-3">Grade</th>
              <th scope="col" class="px-6 py-3">Actions</th>
            </tr>
          </thead>
          <tbody>
            <!-- Loading State -->
            <tr v-if="isLoading">
              <td colspan="7" class="px-6 py-12 text-center">
                <div class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-maneb-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="text-gray-600">Loading students...</span>
                </div>
              </td>
            </tr>
            
            <!-- Student Rows -->
            <tr v-else v-for="student in eligibleStudents" :key="student.id" class="border-b hover:bg-gray-50">
              <td class="px-6 py-4 font-medium text-gray-900">{{ student.studentId }}</td>
              <td class="px-6 py-4 text-black">{{ student.examNumber }}</td>
              <td class="px-6 py-4 text-black">{{ student.fullName }}</td>
              <td class="px-6 py-4 text-black hidden md:table-cell">{{ student.subjectName }}</td>
              <td class="px-6 py-4">
                <div class="flex items-center space-x-2">
                  <input
                    v-if="student.isEditing"
                    type="number"
                    v-model="student.tempScore"
                    min="0"
                    max="100"
                    step="0.1"
                    class="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-maneb-primary focus:border-maneb-primary"
                    @keyup.enter="saveScore(student)"
                    @keyup.escape="cancelEdit(student)"
                    @blur="saveScore(student)"
                    :data-student-id="student.id"
                  />
                  <span v-else class="font-medium" :class="getScoreColor(student.score)">
                    {{ student.score !== null ? student.score + '%' : '-' }}
                  </span>
                  <span v-if="student.hasUnsavedChanges" class="text-orange-500 text-xs">●</span>
                </div>
              </td>
              <td class="px-6 py-4">
                <span v-if="student.score !== null" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" :class="getGradeBadgeClass(student.score)">
                  {{ calculateGrade(student.score) }}
                </span>
                <span v-else class="text-gray-400">-</span>
              </td>
              <td class="px-6 py-4">
                <div class="flex items-center space-x-2">
                  <button
                    v-if="!student.isEditing"
                    @click="startEdit(student)"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded hover:bg-blue-100 focus:ring-2 focus:outline-none focus:ring-blue-300"
                  >
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit
                  </button>
                  
                  <template v-else>
                    <button
                      @click="saveScore(student)"
                      class="inline-flex items-center px-2 py-1 text-xs font-medium text-green-600 bg-green-50 rounded hover:bg-green-100 focus:ring-2 focus:outline-none focus:ring-green-300"
                    >
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Save
                    </button>
                    <button
                      @click="cancelEdit(student)"
                      class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 bg-red-50 rounded hover:bg-red-100 focus:ring-2 focus:outline-none focus:ring-red-300"
                    >
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                      </svg>
                      Cancel
                    </button>
                  </template>
                  
                  <button
                    @click="viewStudent(student)"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 bg-gray-50 rounded hover:bg-gray-100 focus:ring-2 focus:outline-none focus:ring-gray-300"
                  >
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    View
                  </button>

                  <button
                    @click="openNotesModal(student)"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-maneb-primary bg-red-50 rounded hover:bg-red-100 focus:ring-2 focus:outline-none focus:ring-maneb-primary/50"
                  >
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Notes
                    <span v-if="student.hasNotes" class="ml-1 w-2 h-2 bg-maneb-primary rounded-full"></span>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Empty State -->
        <div v-if="!isLoading && eligibleStudents.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No students found</h3>
          <p class="mt-1 text-sm text-gray-500">No students are registered for the selected criteria.</p>
        </div>
      </div>
    </div>

    <!-- Initial State - No Exam Number Selected -->
    <div v-else class="bg-white rounded-lg shadow-sm p-12 text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <h3 class="mt-2 text-lg font-medium text-gray-900">Select Exam Number</h3>
      <p class="mt-1 text-sm text-gray-500">Choose an exam number above to load eligible students for score entry.</p>
    </div>

    <!-- MANEB Validation Modal -->
    <ManebValidation
      :is-open="isValidationModalOpen"
      :score-data="pendingScoreData"
      @close="closeValidationModal"
      @confirmed="handleValidationConfirmed"
    />

    <!-- Student Notes Modal -->
    <div
      v-if="isNotesModalOpen"
      class="fixed top-0 left-0 right-0 z-50 w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full bg-gray-900 bg-opacity-50 flex items-center justify-center"
      @click.self="closeNotesModal"
    >
      <div class="relative w-full max-w-2xl max-h-full">
        <div class="relative bg-white rounded-lg shadow">
          <!-- Modal header -->
          <div class="flex items-start justify-between p-4 border-b rounded-t">
            <h3 class="text-xl font-semibold text-gray-900">
              Student Notes - {{ selectedStudentForNotes?.fullName }}
            </h3>
            <button
              @click="closeNotesModal"
              type="button"
              class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center"
            >
              <svg class="w-3 h-3" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
              </svg>
            </button>
          </div>

          <!-- Modal body -->
          <div class="p-6 space-y-4">
            <!-- Student Info -->
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-gray-600">Student ID:</span>
                  <span class="ml-2 font-medium text-black">{{ selectedStudentForNotes?.studentId }}</span>
                </div>
                <div>
                  <span class="text-gray-600">Exam Number:</span>
                  <span class="ml-2 font-medium text-black">{{ selectedStudentForNotes?.examNumber }}</span>
                </div>
                <div>
                  <span class="text-gray-600">Subject:</span>
                  <span class="ml-2 font-medium text-black">{{ selectedStudentForNotes?.subjectName }}</span>
                </div>
                <div>
                  <span class="text-gray-600">Current Score:</span>
                  <span class="ml-2 font-medium" :class="getScoreColor(selectedStudentForNotes?.score)">
                    {{ selectedStudentForNotes?.score !== null ? selectedStudentForNotes?.score + '%' : 'Not entered' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Notes Input -->
            <div>
              <label for="studentNotes" class="block mb-2 text-sm font-medium text-gray-900">
                Notes
              </label>
              <textarea
                id="studentNotes"
                v-model="currentNotes"
                rows="6"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5"
                placeholder="Enter notes about this student's performance, special circumstances, or other relevant information..."
              ></textarea>
              <p class="mt-1 text-xs text-gray-500">These notes will be saved with the student's record and can be viewed by authorized personnel.</p>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-end space-x-3 pt-4 border-t">
              <button
                @click="closeNotesModal"
                type="button"
                class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900"
              >
                Cancel
              </button>
              <button
                @click="saveNotes"
                type="button"
                :disabled="isSavingNotes"
                class="text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:ring-4 focus:outline-none focus:ring-maneb-primary/50 font-medium rounded-lg text-sm px-5 py-2.5 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                <span v-if="isSavingNotes" class="flex items-center">
                  <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </span>
                <span v-else>Save Notes</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import sweetAlert from '@/utils/sweetAlert'
import ManebValidation from './ManebValidation.vue'

// Props
interface Props {
  selectedFilters: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'edit-filters': []
  'score-updated': [data: any]
}>()

// Types
interface Student {
  id: string
  studentId: string
  examNumber: string
  fullName: string
  subjectName: string
  score: number | null
  tempScore: number | null
  isEditing: boolean
  hasUnsavedChanges: boolean
  notes: string
  hasNotes: boolean
}

// Reactive state
const eligibleStudents = ref<Student[]>([])
const isLoading = ref(false)
const isSaving = ref(false)
const isValidationModalOpen = ref(false)
const pendingScoreData = ref<any>(null)
const isNotesModalOpen = ref(false)
const selectedStudentForNotes = ref<Student | null>(null)
const currentNotes = ref('')
const isSavingNotes = ref(false)
const selectedExamNumber = ref('')
const availableExamNumbers = ref<any[]>([])
const studentsLoaded = ref(false)

// Mock data
const mockStudents: Student[] = [
  {
    id: '1',
    studentId: 'STU001',
    examNumber: 'MATH001',
    fullName: 'John Banda',
    subjectName: 'Mathematics',
    score: 85,
    tempScore: null,
    isEditing: false,
    hasUnsavedChanges: false,
    notes: 'Excellent performance in algebra section.',
    hasNotes: true
  },
  {
    id: '2',
    studentId: 'STU002',
    examNumber: 'MATH001',
    fullName: 'Mary Phiri',
    subjectName: 'Mathematics',
    score: null,
    tempScore: null,
    isEditing: false,
    hasUnsavedChanges: false,
    notes: '',
    hasNotes: false
  },
  {
    id: '3',
    studentId: 'STU003',
    examNumber: 'MATH001',
    fullName: 'Peter Mwale',
    subjectName: 'Mathematics',
    score: 72,
    tempScore: null,
    isEditing: false,
    hasUnsavedChanges: false,
    notes: 'Struggled with geometry questions but showed good understanding of calculus.',
    hasNotes: true
  }
]

// Computed properties
const hasUnsavedChanges = computed(() => {
  return eligibleStudents.value.some(student => student.hasUnsavedChanges)
})

const completedScores = computed(() => {
  return eligibleStudents.value.filter(student => student.score !== null).length
})

// Mock exam numbers data
const mockExamNumbers = [
  { id: '1', name: 'MATH001-2024', subject: 'Mathematics', paper: 'Paper 1' },
  { id: '2', name: 'MATH002-2024', subject: 'Mathematics', paper: 'Paper 2' },
  { id: '3', name: 'ENG001-2024', subject: 'English Language', paper: 'Paper 1' },
  { id: '4', name: 'ENG002-2024', subject: 'English Language', paper: 'Paper 2' },
  { id: '5', name: 'SCI001-2024', subject: 'Physical Science', paper: 'Paper 1' },
  { id: '6', name: 'SCI002-2024', subject: 'Physical Science', paper: 'Paper 2' },
  { id: '7', name: 'BIO001-2024', subject: 'Biology', paper: 'Paper 1' },
  { id: '8', name: 'CHEM001-2024', subject: 'Chemistry', paper: 'Paper 1' }
]

// Methods
const loadAvailableExamNumbers = () => {
  // Filter exam numbers based on selected filters
  // In a real implementation, this would be an API call
  availableExamNumbers.value = mockExamNumbers.filter(exam => {
    // Filter based on subject and paper from selected filters
    return exam.subject === props.selectedFilters?.subjectName &&
           exam.paper === props.selectedFilters?.paperName
  })
}

const loadStudentsForExam = async () => {
  if (!selectedExamNumber.value) return

  isLoading.value = true
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    eligibleStudents.value = [...mockStudents]
    studentsLoaded.value = true

    sweetAlert.success('Students Loaded', `Students loaded for exam ${getSelectedExamNumberName()}`)
  } catch (error) {
    console.error('Error loading students:', error)
    sweetAlert.error('Error', 'Failed to load students')
  } finally {
    isLoading.value = false
  }
}

const onExamNumberChange = () => {
  studentsLoaded.value = false
  eligibleStudents.value = []
}

const getSelectedExamNumberName = () => {
  const exam = availableExamNumbers.value.find(e => e.id === selectedExamNumber.value)
  return exam?.name || ''
}

const calculateGrade = (score: number): string => {
  if (score >= 80) return 'A'
  if (score >= 70) return 'B'
  if (score >= 60) return 'C'
  if (score >= 50) return 'D'
  return 'F'
}

const getScoreColor = (score: number | null): string => {
  if (score === null) return 'text-gray-400'
  if (score >= 80) return 'text-green-600'
  if (score >= 70) return 'text-blue-600'
  if (score >= 60) return 'text-yellow-600'
  if (score >= 50) return 'text-orange-600'
  return 'text-red-600'
}

const getGradeBadgeClass = (score: number): string => {
  if (score >= 80) return 'bg-green-100 text-green-800'
  if (score >= 70) return 'bg-blue-100 text-blue-800'
  if (score >= 60) return 'bg-yellow-100 text-yellow-800'
  if (score >= 50) return 'bg-orange-100 text-orange-800'
  return 'bg-red-100 text-red-800'
}

const startEdit = (student: Student) => {
  student.isEditing = true
  student.tempScore = student.score

  // Auto-focus the input field
  setTimeout(() => {
    const input = document.querySelector(`input[data-student-id="${student.id}"]`) as HTMLInputElement
    if (input) {
      input.focus()
      input.select()
    }
  }, 50)
}

const cancelEdit = (student: Student) => {
  student.isEditing = false
  student.tempScore = null
  student.hasUnsavedChanges = false
}

const saveScore = async (student: Student) => {
  if (student.tempScore !== null && student.tempScore >= 0 && student.tempScore <= 100) {
    // Open MANEB validation modal for SOPs compliance
    pendingScoreData.value = {
      studentId: student.studentId,
      examNumber: student.examNumber,
      subject: student.subjectName,
      score: student.tempScore,
      student: student
    }
    isValidationModalOpen.value = true
  } else {
    sweetAlert.error('Invalid Score', 'Please enter a score between 0 and 100')
  }
}

const saveAllScores = async () => {
  isSaving.value = true
  try {
    // Mock API call to save all changes
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Mark all changes as saved
    eligibleStudents.value.forEach(student => {
      student.hasUnsavedChanges = false
    })
    
    sweetAlert.success('Success', 'All scores have been saved successfully')
  } catch (error) {
    console.error('Error saving scores:', error)
    sweetAlert.error('Error', 'Failed to save scores')
  } finally {
    isSaving.value = false
  }
}

const viewStudent = (student: Student) => {
  sweetAlert.info('Student Details', `Viewing details for ${student.fullName}`)
}

const editFilters = () => {
  emit('edit-filters')
}

const closeValidationModal = () => {
  isValidationModalOpen.value = false
  pendingScoreData.value = null
}

const handleValidationConfirmed = (auditData: any) => {
  const student = pendingScoreData.value?.student
  if (student) {
    // Apply the score after MANEB validation
    student.score = pendingScoreData.value.score
    student.hasUnsavedChanges = true
    student.isEditing = false

    // Emit score update with audit data
    emit('score-updated', {
      studentId: student.id,
      score: student.score,
      examNumber: student.examNumber,
      auditData: auditData
    })

    sweetAlert.success(
      'Score Validated & Saved',
      'The score has been validated according to MANEB SOPs and saved successfully.'
    )
  }

  closeValidationModal()
}

const openNotesModal = (student: Student) => {
  selectedStudentForNotes.value = student
  currentNotes.value = student.notes
  isNotesModalOpen.value = true
}

const closeNotesModal = () => {
  isNotesModalOpen.value = false
  selectedStudentForNotes.value = null
  currentNotes.value = ''
}

const saveNotes = async () => {
  if (!selectedStudentForNotes.value) return

  isSavingNotes.value = true

  try {
    // Mock API call to save notes
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Update the student's notes
    selectedStudentForNotes.value.notes = currentNotes.value
    selectedStudentForNotes.value.hasNotes = currentNotes.value.trim() !== ''

    sweetAlert.success('Notes Saved', 'Student notes have been saved successfully.')
    closeNotesModal()

  } catch (error) {
    console.error('Error saving notes:', error)
    sweetAlert.error('Error', 'Failed to save notes. Please try again.')
  } finally {
    isSavingNotes.value = false
  }
}

// Helper methods for filter names
const getDivisionName = () => props.selectedFilters?.divisionName || 'Unknown Division'
const getDistrictName = () => props.selectedFilters?.districtName || 'Unknown District'
const getCenterName = () => props.selectedFilters?.centerName || 'Unknown Center'
const getExamTypeName = () => props.selectedFilters?.examTypeName || 'Unknown Exam Type'
const getSchoolName = () => props.selectedFilters?.schoolName || 'Unknown School'
const getSubjectName = () => props.selectedFilters?.subjectName || 'Unknown Subject'
const getPaperName = () => props.selectedFilters?.paperName || 'Unknown Paper'
const getScoreTypeName = () => props.selectedFilters?.scoreTypeName || 'Unknown Score Type'

onMounted(() => {
  loadAvailableExamNumbers()
})
</script>

<style scoped>
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary\/50:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}
</style>
