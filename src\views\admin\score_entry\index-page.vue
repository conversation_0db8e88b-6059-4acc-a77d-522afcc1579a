<template>
  <div class="min-h-screen bg-gray-50">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
      <!-- Breadcrumb -->
      <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <a href="/admin/dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-maneb-primary">
              <svg class="w-3 h-3 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
              </svg>
              Admin
            </a>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
              <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Score Entry</span>
            </div>
          </li>
        </ol>
      </nav>

      <!-- Page Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Score Entry</h1>
        <p class="mt-2 text-gray-600">Enter examination scores for registered students</p>
      </div>

      <!-- Progressive Filtering System -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Filter Students</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Exam Type Filter -->
          <div>
            <label for="examType" class="block text-sm font-medium text-gray-700 mb-2">
              Exam Type <span class="text-red-500">*</span>
            </label>
            <select
              id="examType"
              v-model="selectedExamType"
              @change="onExamTypeChange"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5"
            >
              <option value="">Select Exam Type</option>
              <option v-for="examType in examTypes" :key="examType.id" :value="examType.id">
                {{ examType.name }}
              </option>
            </select>
          </div>

          <!-- Exam Number Filter -->
          <div>
            <label for="examNumber" class="block text-sm font-medium text-gray-700 mb-2">
              Exam Number <span class="text-red-500">*</span>
            </label>
            <select
              id="examNumber"
              v-model="selectedExamNumber"
              @change="onExamNumberChange"
              :disabled="!selectedExamType"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 disabled:bg-gray-100 disabled:cursor-not-allowed"
            >
              <option value="">Select Exam Number</option>
              <option v-for="examNumber in availableExamNumbers" :key="examNumber.id" :value="examNumber.id">
                {{ examNumber.number }} - {{ examNumber.subject }}
              </option>
            </select>
          </div>

          <!-- Subject Display -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
            <div class="bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg p-2.5">
              {{ selectedSubject || 'Select exam number first' }}
            </div>
          </div>
        </div>

        <!-- Filter Summary -->
        <div v-if="selectedExamType && selectedExamNumber" class="mt-4 p-4 bg-blue-50 rounded-lg">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-blue-800 text-sm">
              Showing students registered for: <strong>{{ getSelectedExamTypeName() }}</strong> - 
              <strong>{{ getSelectedExamNumberName() }}</strong>
            </span>
          </div>
        </div>
      </div>

      <!-- Students Table -->
      <div v-if="selectedExamType && selectedExamNumber" class="bg-white rounded-lg shadow-sm">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Eligible Students</h3>
            <span class="text-sm text-gray-500">{{ eligibleStudents.length }} students found</span>
          </div>
        </div>

        <div class="overflow-x-auto">
          <table class="w-full text-left text-sm text-gray-500">
            <thead class="bg-gray-50 text-xs uppercase text-gray-700">
              <tr>
                <th scope="col" class="px-6 py-3">Student ID</th>
                <th scope="col" class="px-6 py-3">Full Name</th>
                <th scope="col" class="px-6 py-3 hidden md:table-cell">Email</th>
                <th scope="col" class="px-6 py-3 hidden lg:table-cell">Registration Date</th>
                <th scope="col" class="px-6 py-3">Actions</th>
              </tr>
            </thead>
            <tbody>
              <!-- Loading State -->
              <tr v-if="isLoading">
                <td colspan="5" class="px-6 py-12 text-center">
                  <div class="flex items-center justify-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-maneb-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span class="text-gray-600">Loading students...</span>
                  </div>
                </td>
              </tr>
              <!-- Student Rows -->
              <tr v-else v-for="student in eligibleStudents" :key="student.id" class="border-b hover:bg-gray-50">
                <td class="px-6 py-4 font-medium text-gray-900">{{ student.userName }}</td>
                <td class="px-6 py-4 text-black">{{ student.firstName }} {{ student.lastName }}</td>
                <td class="px-6 py-4 text-black hidden md:table-cell">{{ student.email }}</td>
                <td class="px-6 py-4 text-black hidden lg:table-cell">{{ formatDate(student.createdAt) }}</td>
                <td class="px-6 py-4">
                  <button
                    @click="openScoreModal(student)"
                    class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-maneb-primary rounded-lg hover:bg-maneb-primary-dark focus:ring-4 focus:outline-none focus:ring-maneb-primary/50 transition-colors duration-200"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    <span class="hidden sm:inline">Enter Score</span>
                    <span class="sm:hidden">Score</span>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Empty State -->
          <div v-if="!isLoading && eligibleStudents.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No students found</h3>
            <p class="mt-1 text-sm text-gray-500">No students are registered for the selected exam.</p>
          </div>
        </div>
      </div>

      <!-- Initial State -->
      <div v-else class="bg-white rounded-lg shadow-sm p-12 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
        </svg>
        <h3 class="mt-2 text-lg font-medium text-gray-900">Select Exam Details</h3>
        <p class="mt-1 text-sm text-gray-500">Choose an exam type and number to view eligible students for score entry.</p>
      </div>
    </div>

    <!-- Score Entry Modal -->
    <ScoreEntryModal
      :is-open="isModalOpen"
      :student="selectedStudent"
      :exam-details="examDetails"
      @close="closeScoreModal"
      @success="handleScoreSubmitted"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import ScoreEntryModal from './components/ScoreEntryModal.vue'
import type { UserDto } from '@/interfaces'
import sweetAlert from '@/utils/sweetAlert'

// Types
interface ExamType {
  id: string
  name: string
}

interface ExamNumber {
  id: string
  number: string
  subject: string
  examTypeId: string
}

interface ExamDetails {
  examType: string
  examNumber: string
  subject: string
}

// Reactive state
const selectedExamType = ref('')
const selectedExamNumber = ref('')
const selectedStudent = ref<UserDto | null>(null)
const isModalOpen = ref(false)
const eligibleStudents = ref<UserDto[]>([])
const isLoading = ref(false)

// Mock data - replace with actual API calls
const examTypes = ref<ExamType[]>([
  { id: '1', name: 'MSCE (Malawi School Certificate of Education)' },
  { id: '2', name: 'JCE (Junior Certificate of Education)' },
  { id: '3', name: 'PSLCE (Primary School Leaving Certificate)' }
])

const examNumbers = ref<ExamNumber[]>([
  { id: '1', number: 'ENG001', subject: 'English Language', examTypeId: '1' },
  { id: '2', number: 'MATH001', subject: 'Mathematics', examTypeId: '1' },
  { id: '3', number: 'SCI001', subject: 'Physical Science', examTypeId: '1' },
  { id: '4', number: 'ENG002', subject: 'English Language', examTypeId: '2' },
  { id: '5', number: 'MATH002', subject: 'Mathematics', examTypeId: '2' }
])

// Mock students data
const allStudents = ref<UserDto[]>([
  {
    id: '1',
    userName: 'STU001',
    firstName: 'John',
    lastName: 'Banda',
    email: '<EMAIL>',
    createdAt: '2024-01-15T10:00:00Z',
    status: 'Approved'
  },
  {
    id: '2',
    userName: 'STU002',
    firstName: 'Mary',
    lastName: 'Phiri',
    email: '<EMAIL>',
    createdAt: '2024-01-16T11:00:00Z',
    status: 'Approved'
  }
])

// Computed properties
const availableExamNumbers = computed(() => {
  if (!selectedExamType.value) return []
  return examNumbers.value.filter(exam => exam.examTypeId === selectedExamType.value)
})

const selectedSubject = computed(() => {
  if (!selectedExamNumber.value) return ''
  const exam = examNumbers.value.find(e => e.id === selectedExamNumber.value)
  return exam?.subject || ''
})

const examDetails = computed((): ExamDetails => ({
  examType: getSelectedExamTypeName(),
  examNumber: getSelectedExamNumberName(),
  subject: selectedSubject.value
}))

// Methods
const onExamTypeChange = () => {
  selectedExamNumber.value = ''
  eligibleStudents.value = []
}

const onExamNumberChange = () => {
  if (selectedExamNumber.value) {
    loadEligibleStudents()
  } else {
    eligibleStudents.value = []
  }
}

const loadEligibleStudents = async () => {
  isLoading.value = true
  try {
    // Mock API call delay
    await new Promise(resolve => setTimeout(resolve, 500))

    // Mock implementation - replace with actual API call
    eligibleStudents.value = allStudents.value.filter(student =>
      student.status === 'Approved'
    )
  } catch (error) {
    console.error('Error loading students:', error)
    sweetAlert.error('Error', 'Failed to load eligible students')
  } finally {
    isLoading.value = false
  }
}

const getSelectedExamTypeName = () => {
  const examType = examTypes.value.find(e => e.id === selectedExamType.value)
  return examType?.name || ''
}

const getSelectedExamNumberName = () => {
  const examNumber = examNumbers.value.find(e => e.id === selectedExamNumber.value)
  return examNumber ? `${examNumber.number} - ${examNumber.subject}` : ''
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const openScoreModal = (student: UserDto) => {
  selectedStudent.value = student
  isModalOpen.value = true
}

const closeScoreModal = () => {
  isModalOpen.value = false
  selectedStudent.value = null
}

const handleScoreSubmitted = (studentId: string) => {
  // Remove student from eligible list after score entry
  eligibleStudents.value = eligibleStudents.value.filter(s => s.id !== studentId)
  closeScoreModal()
  
  sweetAlert.success(
    'Score Entered Successfully',
    'The student score has been recorded and the student has been removed from the list.'
  )
}

onMounted(() => {
  // Initialize component
})
</script>

<style scoped>
.bg-maneb-primary {
  background-color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.text-maneb-primary {
  color: #a12c2c;
}

.hover\:text-maneb-primary:hover {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary\/50:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

/* Responsive table improvements */
@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.875rem;
  }
}
</style>
