// API Response Types
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  success: boolean;
  errors?: string[];
}

// Authentication Types
export interface LoginModel {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface AuthResponse {
  token: string;
  refreshToken: string;
  user: UserDto;
  expiresAt: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

// Education Management System Types (based on actual API)
export type UserStatus = number; // 1 = Student, 2 = Admin
export type Gender = 'Male' | 'Female' | 'Other';

export interface UserDto {
  // System fields (from actual API)
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  status?: UserStatus;
  isDeleted?: boolean;

  // Required user info (from actual API)
  firstName: string;
  lastName: string;

  // Personal details (from actual API)
  gender?: Gender;
  idNumber?: string;
  idType?: string;
  dateOfBirth?: Date;
  role?: string;
  email?: string;
  userName?: string;
  fullName?: string; // Read-only computed field
}

export interface CreateUserRequest extends Omit<UserDto, 'id' | 'createdBy' | 'dateCreated' | 'fullName'> {}
export interface UpdateUserRequest extends Partial<CreateUserRequest> {}

// API Client Types
export interface ApiClientConfig {
  baseURL: string;
  timeout?: number;
}

export interface RequestConfig {
  headers?: Record<string, string>;
  params?: Record<string, any>;
}

// Export table interfaces
export * from './table';