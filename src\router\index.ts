import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/store'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/auth/login',
    },
    {
      path: '/auth',
      children: [
        {
          path: 'login',
          name: 'auth.login',
          component: () => import('@/views/auth/login/index-page.vue'),
          meta: {
            title: 'Login',
            requiresGuest: false, // Temporarily allow authenticated users for debugging
          },
        },
        {
          path: 'register',
          name: 'auth.register',
          component: () => import('@/views/auth/register/index-page.vue'),
          meta: {
            title: 'Register',
          },
        },
        {
          path: 'forgot-password',
          name: 'auth.forgot-password',
          component: () => import('@/views/auth/forgot_password/index-page.vue'),
          meta: {
            title: 'Forgot Password',
          },
        },
      ],
    },
    {
      path: "/admin",
      meta: {
        layout: 'sigelegeLayoutPage',
        requiresAuth: true,
        requiresAdmin: true, // Require admin permissions (email-based)
      },
      children: [
        {
          path: 'dashboard',
          name: 'admin.dashboard',
          component: () => import('@/views/admin/dashboard/index-page.vue'),
          meta: {
            title: 'Administrator Dashboard'
          }
        },
        {
          path: 'user-management',
          name: 'admin.user-management',
          // component: () => import('@/views/admin/user_management/index-page.vue'),
          // meta: {
          //   title: 'User Management',
          //   // layout: 'sigelegeLayoutPage'
          // },
          children: [
            {
              path: 'users',
              name: 'admin.user-management.users',
              component: () => import('@/views/admin/user_management/users/index-page.vue'),
              meta: {
                title: 'Users',
                // layout: 'sigelegeLayoutPage'
              }
            },
            {
              path: 'workspaces',
              name: 'admin.user-management.workspaces',
              component: () => import('@/views/admin/user_management/workspaces/index-page.vue'),
              meta: {
                title: 'Workspaces',
                // layout: 'sigelegeLayoutPage'
              }
            },
            {
              path: 'roles',
              name: 'admin.user-management.roles',
              component: () => import('@/views/admin/user_management/roles/index-page.vue'),
              meta: {
                title: 'Roles',
                // layout: 'sigelegeLayoutPage'
              }
            },
            {
              path: 'permissions',
              name: 'admin.user-management.permissions',
              component: () => import('@/views/admin/user_management/permissions/index-page.vue'),
              meta: {
                title: 'Permissions',
                // layout: 'sigelegeLayoutPage'
              }
            },
            {
              path: 'audits',
              name: 'admin.user-management.audits',
              component: () => import('@/views/admin/user_management/audits/index-page.vue'),
              meta: {
                title: 'Audits',
                // layout: 'sigelegeLayoutPage'
              }
            }
          ]
        }
      ]
    },
    {
      path: "/student",
      meta: {
        requiresAuth: true,
        requiresApproval: false, // Allow access for all authenticated users
      },
      children: [
        {
          path: 'dashboard',
          name: 'student.dashboard',
          component: () => import('@/views/student/dashboard/index-page.vue'),
          meta: {
            title: 'Student Dashboard'
          }
        },
        {
          path: 'results',
          name: 'student.results',
          component: () => import('@/views/student/results/index-page.vue'),
          meta: {
            title: 'My Results'
          }
        },
        {
          path: 'certificates',
          name: 'student.certificates',
          component: () => import('@/views/student/certificates/index-page.vue'),
          meta: {
            title: 'My Certificates'
          }
        },
        {
          path: 'profile',
          name: 'student.profile',
          component: () => import('@/views/student/profile/index-page.vue'),
          meta: {
            title: 'My Profile'
          }
        },
        {
          path: 'schedule',
          name: 'student.schedule',
          component: () => import('@/views/student/schedule/index-page.vue'),
          meta: {
            title: 'Exam Schedule'
          }
        }
      ]
    }
  ],
})

// Navigation Guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  console.log('Router guard - navigating to:', to.path)
  console.log('Router guard - authenticated:', authStore.isAuthenticated)
  console.log('Router guard - user:', authStore.user)

  // Initialize auth store if not already done
  if (!authStore.isAuthenticated && !authStore.isLoading) {
    authStore.initializeAuth()
  }

  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      // Redirect to login if not authenticated
      return next({ name: 'auth.login', query: { redirect: to.fullPath } })
    }

    // Check if route requires admin permissions
    if (to.meta.requiresAdmin && !authStore.isAdmin) {
      // Redirect non-admin users to student dashboard
      return next({ name: 'student.dashboard' })
    }

    // Check if route requires approval
    if (to.meta.requiresApproval && !authStore.isApproved) {
      // Redirect to a pending approval page or dashboard
      return next({ name: 'auth.login' }) // You can create a pending approval page
    }
  }

  // Check if route is for guests only (like login page)
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    // Redirect authenticated users to their appropriate dashboard
    const dashboardRoute = authStore.getDashboardRoute
    return next(dashboardRoute)
  }

  // Role-based access control
  if (to.meta.requiresAuth) {
    // Check if admin trying to access student routes
    if (to.path.startsWith('/student') && authStore.isAdmin) {
      return next({ name: 'admin.dashboard' })
    }

    // Check if student trying to access admin routes (except dashboard which is allowed)
    if (to.path.startsWith('/admin') && !authStore.isAdmin && to.name !== 'admin.dashboard') {
      return next({ name: 'student.dashboard' })
    }
  }

  next()
})

export default router
