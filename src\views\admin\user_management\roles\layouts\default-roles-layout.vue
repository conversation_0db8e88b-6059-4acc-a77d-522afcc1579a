<template>
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 spacing-2">
        <BreadcrumbsActions :breadcrumbs="roleBreadcrumbs" />
        <DefaultTable
          :columns="roleColumns"
          :data="roleData"
          title="Roles"
          :filterOptions="roleFilterOptions"
          :radioFilters="roleRadioFilters"
          addButtonLabel="Add New Role"
          :showAddButton="true"
        />
        <Footer />
      </div>
</template>

<script setup lang="ts">
import BreadcrumbsActions from '@/components/UI/breadcrumbs/breadcrumbs_actions.vue'

const roleBreadcrumbs = [
  { name: 'Admin', href: '/admin', current: false },
  { name: 'User Management', href: '/admin/user-management', current: false },
  { name: 'Roles', href: '/admin/user-management/roles', current: true },
];
import DefaultTable from '@/components/UI/tables/default-table.vue';
import Footer from '@/components/UI/footers/footer-one.vue';
import type { TableColumn, TableRowData } from '@/interfaces/table';

const roleColumns: TableColumn[] = [
  { key: 'role_id', label: 'Role ID', sortable: true },
  { key: 'name', label: 'Role Name', sortable: true },
  { key: 'description', label: 'Description', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
];

const roleData: TableRowData[] = [
  { role_id: 'ROL001', name: 'Administrator', description: 'Full access to all features', status: 'Active' },
  { role_id: 'ROL002', name: 'Editor', description: 'Can create and edit content', status: 'Active' },
  { role_id: 'ROL003', name: 'Viewer', description: 'Can view content only', status: 'Active' },
  { role_id: 'ROL004', name: 'Guest', description: 'Limited access', status: 'Inactive' },
];

const roleFilterOptions = [
  { label: 'Last 7 days', value: 'last7days' },
  { label: 'Last 30 days', value: 'last30days' },
  { label: 'All time', value: 'alltime' },
];

const roleRadioFilters = [
  { label: 'All Roles', value: 'All' },
  { label: 'Active Roles', value: 'Active' },
  { label: 'Inactive Roles', value: 'Inactive' },
];

</script>
