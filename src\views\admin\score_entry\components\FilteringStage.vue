<template>
  <div class="bg-white rounded-lg shadow-sm p-8">
    <div class="mb-8">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">Select Examination Criteria</h2>
      <p class="text-gray-600">Please complete all required filters to proceed to score entry</p>
    </div>

    <!-- Progress Indicator -->
    <div class="mb-8">
      <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
        <span>Progress</span>
        <span>{{ completedFilters }}/7 completed</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div 
          class="bg-maneb-primary h-2 rounded-full transition-all duration-300"
          :style="{ width: `${(completedFilters / 7) * 100}%` }"
        ></div>
      </div>
    </div>

    <!-- Hierarchical Filters -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <!-- Division Filter -->
      <div>
        <label for="division" class="block text-sm font-medium text-gray-700 mb-2">
          Division <span class="text-red-500">*</span>
        </label>
        <select
          id="division"
          v-model="filters.division"
          @change="onDivisionChange"
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5"
          :disabled="isLoading"
        >
          <option value="">Select Division</option>
          <option v-for="division in divisions" :key="division.id" :value="division.id">
            {{ division.name }}
          </option>
        </select>
      </div>

      <!-- District Filter -->
      <div>
        <label for="district" class="block text-sm font-medium text-gray-700 mb-2">
          District <span class="text-red-500">*</span>
        </label>
        <select
          id="district"
          v-model="filters.district"
          @change="onDistrictChange"
          :disabled="!filters.division || isLoading"
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 disabled:bg-gray-100 disabled:cursor-not-allowed"
        >
          <option value="">Select District</option>
          <option v-for="district in availableDistricts" :key="district.id" :value="district.id">
            {{ district.name }}
          </option>
        </select>
      </div>

      <!-- Center Filter -->
      <div>
        <label for="center" class="block text-sm font-medium text-gray-700 mb-2">
          Center <span class="text-red-500">*</span>
        </label>
        <select
          id="center"
          v-model="filters.center"
          @change="onCenterChange"
          :disabled="!filters.district || isLoading"
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 disabled:bg-gray-100 disabled:cursor-not-allowed"
        >
          <option value="">Select Center</option>
          <option v-for="center in availableCenters" :key="center.id" :value="center.id">
            {{ center.name }}
          </option>
        </select>
      </div>

      <!-- School Filter -->
      <div>
        <label for="school" class="block text-sm font-medium text-gray-700 mb-2">
          School <span class="text-red-500">*</span>
        </label>
        <select
          id="school"
          v-model="filters.school"
          @change="onSchoolChange"
          :disabled="!filters.center || isLoading"
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 disabled:bg-gray-100 disabled:cursor-not-allowed"
        >
          <option value="">Select School</option>
          <option v-for="school in availableSchools" :key="school.id" :value="school.id">
            {{ school.name }}
          </option>
        </select>
      </div>

      <!-- Subject Filter -->
      <div>
        <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
          Subject <span class="text-red-500">*</span>
        </label>
        <select
          id="subject"
          v-model="filters.subject"
          @change="onSubjectChange"
          :disabled="!filters.school || isLoading"
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 disabled:bg-gray-100 disabled:cursor-not-allowed"
        >
          <option value="">Select Subject</option>
          <option v-for="subject in availableSubjects" :key="subject.id" :value="subject.id">
            {{ subject.name }}
          </option>
        </select>
      </div>

      <!-- Exam Type Filter -->
      <div>
        <label for="examType" class="block text-sm font-medium text-gray-700 mb-2">
          Exam Type <span class="text-red-500">*</span>
        </label>
        <select
          id="examType"
          v-model="filters.examType"
          @change="onExamTypeChange"
          :disabled="!filters.subject || isLoading"
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 disabled:bg-gray-100 disabled:cursor-not-allowed"
        >
          <option value="">Select Exam Type</option>
          <option v-for="examType in availableExamTypes" :key="examType.id" :value="examType.id">
            {{ examType.name }}
          </option>
        </select>
      </div>

      <!-- Exam Number Filter -->
      <div>
        <label for="examNumber" class="block text-sm font-medium text-gray-700 mb-2">
          Exam Number <span class="text-red-500">*</span>
        </label>
        <select
          id="examNumber"
          v-model="filters.examNumber"
          @change="onExamNumberChange"
          :disabled="!filters.examType || isLoading"
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 disabled:bg-gray-100 disabled:cursor-not-allowed"
        >
          <option value="">Select Exam Number</option>
          <option v-for="examNumber in availableExamNumbers" :key="examNumber.id" :value="examNumber.id">
            {{ examNumber.number }}
          </option>
        </select>
      </div>
    </div>

    <!-- Filter Summary -->
    <div v-if="completedFilters > 0" class="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <h3 class="text-sm font-medium text-blue-900 mb-2">Current Selection:</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 text-sm">
        <div v-if="filters.division" class="text-blue-800">
          <span class="font-medium">Division:</span> {{ getDivisionName(filters.division) }}
        </div>
        <div v-if="filters.district" class="text-blue-800">
          <span class="font-medium">District:</span> {{ getDistrictName(filters.district) }}
        </div>
        <div v-if="filters.center" class="text-blue-800">
          <span class="font-medium">Center:</span> {{ getCenterName(filters.center) }}
        </div>
        <div v-if="filters.school" class="text-blue-800">
          <span class="font-medium">School:</span> {{ getSchoolName(filters.school) }}
        </div>
        <div v-if="filters.subject" class="text-blue-800">
          <span class="font-medium">Subject:</span> {{ getSubjectName(filters.subject) }}
        </div>
        <div v-if="filters.examType" class="text-blue-800">
          <span class="font-medium">Exam Type:</span> {{ getExamTypeName(filters.examType) }}
        </div>
        <div v-if="filters.examNumber" class="text-blue-800">
          <span class="font-medium">Exam Number:</span> {{ getExamNumberName(filters.examNumber) }}
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex items-center justify-between pt-6 border-t border-gray-200">
      <button
        @click="resetFilters"
        type="button"
        class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10"
        :disabled="isLoading || completedFilters === 0"
      >
        Reset Filters
      </button>
      
      <button
        @click="proceedToScoreEntry"
        type="button"
        class="text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:ring-4 focus:outline-none focus:ring-maneb-primary/50 font-medium rounded-lg text-sm px-5 py-2.5 text-center"
        :disabled="!allFiltersSelected || isLoading"
      >
        <span v-if="isLoading" class="flex items-center">
          <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Loading...
        </span>
        <span v-else>Continue to Score Entry</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import sweetAlert from '@/utils/sweetAlert'

// Props
interface Props {
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
})

// Emits
const emit = defineEmits<{
  'filters-selected': [filters: any]
}>()

// Types
interface FilterOption {
  id: string
  name: string
  parentId?: string
}

// Reactive state
const filters = ref({
  division: '',
  district: '',
  center: '',
  school: '',
  subject: '',
  examType: '',
  examNumber: ''
})

// Mock data - replace with actual API calls
const divisions = ref<FilterOption[]>([
  { id: '1', name: 'Northern Division' },
  { id: '2', name: 'Central Division' },
  { id: '3', name: 'Southern Division' }
])

const districts = ref<FilterOption[]>([
  { id: '1', name: 'Lilongwe', parentId: '2' },
  { id: '2', name: 'Blantyre', parentId: '3' },
  { id: '3', name: 'Mzuzu', parentId: '1' },
  { id: '4', name: 'Kasungu', parentId: '2' },
  { id: '5', name: 'Zomba', parentId: '3' }
])

const centers = ref<FilterOption[]>([
  { id: '1', name: 'Lilongwe Center 1', parentId: '1' },
  { id: '2', name: 'Lilongwe Center 2', parentId: '1' },
  { id: '3', name: 'Blantyre Center 1', parentId: '2' },
  { id: '4', name: 'Mzuzu Center 1', parentId: '3' }
])

const schools = ref<FilterOption[]>([
  { id: '1', name: 'Lilongwe Secondary School', parentId: '1' },
  { id: '2', name: 'Kamuzu Academy', parentId: '1' },
  { id: '3', name: 'Blantyre Secondary School', parentId: '3' },
  { id: '4', name: 'Mzuzu Secondary School', parentId: '4' }
])

const subjects = ref<FilterOption[]>([
  { id: '1', name: 'Mathematics', parentId: '1' },
  { id: '2', name: 'English Language', parentId: '1' },
  { id: '3', name: 'Physical Science', parentId: '1' },
  { id: '4', name: 'Biology', parentId: '2' },
  { id: '5', name: 'Chemistry', parentId: '3' }
])

const examTypes = ref<FilterOption[]>([
  { id: '1', name: 'MSCE (Malawi School Certificate of Education)', parentId: '1' },
  { id: '2', name: 'JCE (Junior Certificate of Education)', parentId: '2' },
  { id: '3', name: 'PSLCE (Primary School Leaving Certificate)', parentId: '3' }
])

const examNumbers = ref<FilterOption[]>([
  { id: '1', name: 'MATH001', parentId: '1' },
  { id: '2', name: 'ENG001', parentId: '2' },
  { id: '3', name: 'SCI001', parentId: '1' },
  { id: '4', name: 'BIO001', parentId: '2' }
])

// Computed properties
const availableDistricts = computed(() => 
  districts.value.filter(d => d.parentId === filters.value.division)
)

const availableCenters = computed(() => 
  centers.value.filter(c => c.parentId === filters.value.district)
)

const availableSchools = computed(() => 
  schools.value.filter(s => s.parentId === filters.value.center)
)

const availableSubjects = computed(() => 
  subjects.value.filter(s => s.parentId === filters.value.school)
)

const availableExamTypes = computed(() => 
  examTypes.value.filter(e => e.parentId === filters.value.subject)
)

const availableExamNumbers = computed(() => 
  examNumbers.value.filter(e => e.parentId === filters.value.examType)
)

const completedFilters = computed(() => {
  return Object.values(filters.value).filter(value => value !== '').length
})

const allFiltersSelected = computed(() => {
  return completedFilters.value === 7
})

// Helper methods
const getDivisionName = (id: string) => divisions.value.find(d => d.id === id)?.name || ''
const getDistrictName = (id: string) => districts.value.find(d => d.id === id)?.name || ''
const getCenterName = (id: string) => centers.value.find(c => c.id === id)?.name || ''
const getSchoolName = (id: string) => schools.value.find(s => s.id === id)?.name || ''
const getSubjectName = (id: string) => subjects.value.find(s => s.id === id)?.name || ''
const getExamTypeName = (id: string) => examTypes.value.find(e => e.id === id)?.name || ''
const getExamNumberName = (id: string) => examNumbers.value.find(e => e.id === id)?.name || ''

// Event handlers
const onDivisionChange = () => {
  filters.value.district = ''
  filters.value.center = ''
  filters.value.school = ''
  filters.value.subject = ''
  filters.value.examType = ''
  filters.value.examNumber = ''
}

const onDistrictChange = () => {
  filters.value.center = ''
  filters.value.school = ''
  filters.value.subject = ''
  filters.value.examType = ''
  filters.value.examNumber = ''
}

const onCenterChange = () => {
  filters.value.school = ''
  filters.value.subject = ''
  filters.value.examType = ''
  filters.value.examNumber = ''
}

const onSchoolChange = () => {
  filters.value.subject = ''
  filters.value.examType = ''
  filters.value.examNumber = ''
}

const onSubjectChange = () => {
  filters.value.examType = ''
  filters.value.examNumber = ''
}

const onExamTypeChange = () => {
  filters.value.examNumber = ''
}

const onExamNumberChange = () => {
  // Final filter selected
}

const resetFilters = () => {
  filters.value = {
    division: '',
    district: '',
    center: '',
    school: '',
    subject: '',
    examType: '',
    examNumber: ''
  }
}

const proceedToScoreEntry = async () => {
  if (!allFiltersSelected.value) {
    sweetAlert.error('Incomplete Selection', 'Please select all required filters before proceeding.')
    return
  }

  // Validate filter combination
  try {
    const filterData = {
      division: filters.value.division,
      district: filters.value.district,
      center: filters.value.center,
      school: filters.value.school,
      subject: filters.value.subject,
      examType: filters.value.examType,
      examNumber: filters.value.examNumber,
      // Add readable names for display
      divisionName: getDivisionName(filters.value.division),
      districtName: getDistrictName(filters.value.district),
      centerName: getCenterName(filters.value.center),
      schoolName: getSchoolName(filters.value.school),
      subjectName: getSubjectName(filters.value.subject),
      examTypeName: getExamTypeName(filters.value.examType),
      examNumberName: getExamNumberName(filters.value.examNumber)
    }

    emit('filters-selected', filterData)
  } catch (error) {
    console.error('Error processing filters:', error)
    sweetAlert.error('Error', 'Failed to process filter selection. Please try again.')
  }
}
</script>

<style scoped>
.bg-maneb-primary {
  background-color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary\/50:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}
</style>
