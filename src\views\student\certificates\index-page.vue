<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-7xl mx-auto">
      <div class="bg-white rounded-lg shadow-sm p-8">
        <h1 class="text-2xl font-bold text-gray-900 mb-4">My Certificates</h1>
        <div class="text-center py-12">
          <!-- MANEB Themed Certificate Icon -->
          <div class="mx-auto w-20 h-20 bg-red-50 rounded-full flex items-center justify-center mb-6">
            <svg class="h-10 w-10" fill="none" stroke="#a12c2c" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Certificates Coming Soon</h3>
          <p class="text-gray-500">Download and view your certificates here once they become available.</p>
          <router-link to="/student/dashboard" class="mt-4 inline-block text-maneb-primary hover-text-maneb-primary-dark transition-colors duration-200">
            ← Back to Dashboard
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Placeholder component for student certificates
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.hover-text-maneb-primary-dark:hover {
  color: #8b2424;
}
</style>
