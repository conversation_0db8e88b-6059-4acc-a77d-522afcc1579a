import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { userService } from '@/services';
import type { UserDto, CreateUserRequest, UpdateUserRequest, UserStatus } from '@/interfaces';

export const useUserStore = defineStore('user', () => {
  // State
  const users = ref<UserDto[]>([]);
  const currentUser = ref<UserDto | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const searchQuery = ref('');
  const statusFilter = ref<UserStatus | 'All'>('All');
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  // Getters
  const filteredUsers = computed(() => {
    let filtered = users.value;

    // Filter by search query
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(user => 
        user.firstName?.toLowerCase().includes(query) ||
        user.lastName?.toLowerCase().includes(query) ||
        user.email?.toLowerCase().includes(query) ||
        user.fullName?.toLowerCase().includes(query)
      );
    }

    // Filter by status
    if (statusFilter.value !== 'All') {
      filtered = filtered.filter(user => user.status === statusFilter.value);
    }

    return filtered;
  });

  const usersByStatus = computed(() => {
    return {
      unapproved: users.value.filter(u => u.status === 'Unapproved').length,
      approved: users.value.filter(u => u.status === 'Approved').length,
      secondApproved: users.value.filter(u => u.status === 'SecondApproved').length,
      rejected: users.value.filter(u => u.status === 'Rejected').length,
    };
  });

  const totalUsers = computed(() => users.value.length);
  const activeUsers = computed(() => users.value.filter(u => !u.isDeleted).length);

  // Actions
  const fetchUsers = async (): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      users.value = await userService.getAllUsers();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch users';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchUsersPaginated = async (page: number = 1, limit: number = 10): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      
      const response = await userService.getUsersPaginated(page, limit);
      users.value = response.users;
      pagination.value = {
        page: response.page,
        limit,
        total: response.total,
        totalPages: response.totalPages
      };
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch users';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchUserById = async (id: string): Promise<UserDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const user = await userService.getUserById(id);
      currentUser.value = user;
      return user;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch user';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createUser = async (userData: CreateUserRequest): Promise<UserDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const newUser = await userService.createUser(userData);
      users.value.push(newUser);
      return newUser;
    } catch (err: any) {
      error.value = err.message || 'Failed to create user';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateUser = async (id: string, userData: UpdateUserRequest): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await userService.updateUser(id, userData);
      
      // Update user in local state
      const index = users.value.findIndex(u => u.id === id);
      if (index !== -1) {
        users.value[index] = { ...users.value[index], ...userData };
      }
      
      // Update current user if it's the same
      if (currentUser.value?.id === id) {
        currentUser.value = { ...currentUser.value, ...userData };
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to update user';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteUser = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await userService.deleteUser(id);
      
      // Remove user from local state
      users.value = users.value.filter(u => u.id !== id);
      
      // Clear current user if it's the deleted one
      if (currentUser.value?.id === id) {
        currentUser.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete user';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateUserStatus = async (id: string, status: UserStatus): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await userService.updateUserStatus(id, status);
      
      // Update user status in local state
      const index = users.value.findIndex(u => u.id === id);
      if (index !== -1) {
        users.value[index].status = status;
      }
      
      // Update current user if it's the same
      if (currentUser.value?.id === id) {
        currentUser.value.status = status;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to update user status';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const searchUsers = async (query: string): Promise<void> => {
    searchQuery.value = query;
    if (query) {
      try {
        isLoading.value = true;
        error.value = null;
        users.value = await userService.searchUsers(query);
      } catch (err: any) {
        error.value = err.message || 'Failed to search users';
        throw err;
      } finally {
        isLoading.value = false;
      }
    } else {
      await fetchUsers();
    }
  };

  const setStatusFilter = (status: UserStatus | 'All'): void => {
    statusFilter.value = status;
  };

  const clearError = (): void => {
    error.value = null;
  };

  const clearCurrentUser = (): void => {
    currentUser.value = null;
  };

  return {
    // State
    users,
    currentUser,
    isLoading,
    error,
    searchQuery,
    statusFilter,
    pagination,
    
    // Getters
    filteredUsers,
    usersByStatus,
    totalUsers,
    activeUsers,
    
    // Actions
    fetchUsers,
    fetchUsersPaginated,
    fetchUserById,
    createUser,
    updateUser,
    deleteUser,
    updateUserStatus,
    searchUsers,
    setStatusFilter,
    clearError,
    clearCurrentUser,
  };
});
