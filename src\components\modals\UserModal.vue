<template>
  <!-- Flowbite Pro Modal -->
  <div 
    v-if="isOpen"
    id="user-modal" 
    tabindex="-1" 
    aria-hidden="true" 
    class="fixed top-0 left-0 right-0 z-50 w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full bg-gray-900 bg-opacity-50 flex items-center justify-center"
    @click.self="closeModal"
  >
    <div class="relative w-full max-w-4xl max-h-full">
      <!-- Modal content -->
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-800">
        <!-- Modal header -->
        <div class="flex items-start justify-between p-4 border-b rounded-t dark:border-gray-600">
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
            {{ user ? 'Edit User' : 'Create New User' }}
          </h3>
          <button 
            @click="closeModal"
            type="button" 
            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
          >
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
            <span class="sr-only">Close modal</span>
          </button>
        </div>
        
        <!-- Modal body -->
        <div class="max-h-[70vh] overflow-y-auto">
          <UserForm 
            :user="user"
            :is-open="isOpen"
            @close="closeModal"
            @success="handleSuccess"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue'
import UserForm from '@/components/forms/UserForm.vue'
import type { UserDto } from '@/interfaces'

// Props
interface Props {
  isOpen: boolean
  user?: UserDto | null
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  user: null
})

// Emits
const emit = defineEmits<{
  close: []
  success: [user: UserDto]
}>()

// Methods
const closeModal = () => {
  emit('close')
}

const handleSuccess = (user: UserDto) => {
  emit('success', user)
}

// Handle body scroll when modal is open
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = 'auto'
  }
})
</script>

<style scoped>
/* Ensure modal appears above other content */
.z-50 {
  z-index: 50;
}
</style>
